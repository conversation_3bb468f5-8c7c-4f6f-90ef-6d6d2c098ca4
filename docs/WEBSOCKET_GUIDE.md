# WebSocket Streaming Guide

## Overview

StalkAPI provides real-time WebSocket streaming for live KOL (Key Opinion Leader) trading data. This guide covers connection, authentication, subscription management, and message handling.

## Connection Details

### Base WebSocket URL
```
ws://localhost:3001/ws          # Development
wss://your-domain.com/ws        # Production (SSL)
```

### Authentication

StalkAPI WebSocket connections use API key authentication:

```javascript
const ws = new WebSocket('ws://localhost:3001/ws?apiKey=YOUR_API_KEY');
```

For production:
```javascript
const ws = new WebSocket('wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY');
```

## Available Streams

| Stream | Description | Credits per Message | Tier Required |
|--------|-------------|-------------------|---------------|
| `kol-feed` | Real-time KOL trading activity | 2 | Basic+ |

## Message Protocol

### Client to Server Messages

#### Subscribe to Stream
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "kol-feed"
  }
}
```

#### Unsubscribe from Stream
```json
{
  "type": "unsubscribe", 
  "payload": {
    "stream": "kol-feed"
  }
}
```

#### Ping (Keep Alive)
```json
{
  "type": "ping"
}
```

### Server to Client Messages

#### Connection Established
```json
{
  "type": "connected",
  "connectionId": "uuid-string",
  "sessionId": "uuid-string", 
  "message": "WebSocket connection established"
}
```

#### Subscription Confirmed
```json
{
  "type": "subscribed",
  "stream": "kol-feed",
  "message": "Successfully subscribed to kol-feed"
}
```

#### Unsubscription Confirmed
```json
{
  "type": "unsubscribed",
  "stream": "kol-feed",
  "message": "Successfully unsubscribed from kol-feed"
}
```

#### Pong Response
```json
{
  "type": "pong",
  "timestamp": 1748926077000
}
```

#### Stream Data
```json
{
  "type": "stream_data",
  "stream": "kol-feed",
  "data": {
    "timestamp": 1748923344035,
    "kol_label": "TraderSZ",
    "wallet": "private",
    "kol_avatar": null,
    "tokenIn": {
      "symbol": "SOL",
      "name": "Wrapped SOL",
      "logo": "https://...",
      "tokenAmountString": "13.0",
      "amount": 13.0,
      "tokenInAmountUsd": 2095.16,
      "price": 161.17,
      "mint": "So11111111111111111111111111111111111111112"
    },
    "tokenOut": {
      "symbol": "BONK",
      "name": "Bonk",
      "logo": "https://...",
      "tokenAmountString": "45.2M",
      "amount": 45200000,
      "tokenOutAmountUsd": 1987.45,
      "price": 0.000044,
      "mint": "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263"
    },
    "signature": "private",
    "transactionType": "buy",
    "chain": "solana",
    "socials": []
  },
  "timestamp": 1748926077000
}
```

#### Error Messages
```json
{
  "type": "error",
  "error": "Access denied to stream: kol-feed",
  "timestamp": 1748926077000
}
```

#### Credit Warning
```json
{
  "type": "credit_warning",
  "message": "Insufficient credits for stream: kol-feed",
  "credits_remaining": 0,
  "credits_required": 2,
  "timestamp": 1748926077000
}
```

## Implementation Examples

### JavaScript/Node.js
```javascript
const WebSocket = require('ws');

// Connect with JWT token
const ws = new WebSocket('ws://localhost:3001/ws?token=YOUR_JWT_TOKEN');

ws.on('open', () => {
    console.log('Connected to StalkAPI WebSocket');
    
    // Subscribe to KOL feed
    ws.send(JSON.stringify({
        type: 'subscribe',
        payload: { stream: 'kol-feed' }
    }));
});

ws.on('message', (data) => {
    const message = JSON.parse(data.toString());
    
    switch (message.type) {
        case 'connected':
            console.log('Connection established:', message.message);
            break;
            
        case 'subscribed':
            console.log(`Subscribed to: ${message.stream}`);
            break;
            
        case 'stream_data':
            if (message.stream === 'kol-feed') {
                console.log('KOL Trade:', {
                    trader: message.data.kol_label,
                    type: message.data.transactionType,
                    tokenIn: message.data.tokenIn.symbol,
                    tokenOut: message.data.tokenOut.symbol,
                    amountUsd: message.data.tokenInAmountUsd
                });
            }
            break;
            
        case 'error':
            console.error('WebSocket error:', message.error);
            break;
            
        case 'credit_warning':
            console.warn('Credit warning:', message.message);
            break;
    }
});

ws.on('error', (error) => {
    console.error('WebSocket error:', error);
});

ws.on('close', (code, reason) => {
    console.log(`WebSocket closed: ${code} - ${reason}`);
});

// Keep connection alive
setInterval(() => {
    if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type: 'ping' }));
    }
}, 30000);
```

### Python
```python
import asyncio
import websockets
import json

async def connect_to_stalkapi():
    uri = "ws://localhost:3001/ws?token=YOUR_JWT_TOKEN"
    
    async with websockets.connect(uri) as websocket:
        print("Connected to StalkAPI WebSocket")
        
        # Subscribe to KOL feed
        await websocket.send(json.dumps({
            "type": "subscribe",
            "payload": {"stream": "kol-feed"}
        }))
        
        async for message in websocket:
            data = json.loads(message)
            
            if data["type"] == "stream_data" and data["stream"] == "kol-feed":
                trade = data["data"]
                print(f"KOL Trade: {trade['kol_label']} {trade['transactionType']} "
                      f"{trade['tokenIn']['symbol']} -> {trade['tokenOut']['symbol']} "
                      f"(${trade['tokenInAmountUsd']:.2f})")
            
            elif data["type"] == "error":
                print(f"Error: {data['error']}")

# Run the client
asyncio.run(connect_to_stalkapi())
```

## Credit Management

### Credit Consumption
- **Connection**: Free (no credits consumed)
- **Stream Messages**: 2 credits per message for `kol-feed`
- **Insufficient Credits**: Connection remains open, but stream data is replaced with credit warnings

### Credit Monitoring
Use the `/auth/profile` endpoint to check remaining credits:
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "http://localhost:3001/auth/profile"
```

## Best Practices

### 1. Connection Management
- Implement reconnection logic for production use
- Use ping/pong for keep-alive (every 30 seconds recommended)
- Handle connection errors gracefully

### 2. Message Handling
- Always parse JSON messages in try-catch blocks
- Implement proper error handling for all message types
- Log important events for debugging

### 3. Credit Optimization
- Monitor credit usage regularly
- Implement credit warnings handling
- Consider upgrading tier for high-volume usage

### 4. Security
- Use WSS (WebSocket Secure) in production
- Rotate API keys regularly
- Validate all incoming messages

## Troubleshooting

### Common Issues

#### Authentication Failed
```json
{"type": "error", "error": "Authentication failed"}
```
**Solution**: Check your JWT token or API key validity

#### Access Denied to Stream
```json
{"type": "error", "error": "Access denied to stream: kol-feed"}
```
**Solution**: Verify your tier has access to the requested stream

#### Connection Drops
**Solution**: Implement reconnection logic with exponential backoff

### Testing WebSocket Connection

Use `wscat` for quick testing:
```bash
# Install wscat
npm install -g wscat

# Connect and test
wscat -c "ws://localhost:3001/ws?token=YOUR_JWT_TOKEN"

# Send subscription message
{"type": "subscribe", "payload": {"stream": "kol-feed"}}
```

## Rate Limits

- **Connections per User**: Based on tier (Basic: 3, Premium: 10, Enterprise: 50)
- **Messages per Second**: No limit on client messages
- **Stream Data**: Real-time as available

## Support

For WebSocket-related issues:
1. Check connection logs
2. Verify authentication credentials  
3. Monitor credit balance
4. Review tier permissions
5. Contact support with connection details

---

**Next Steps**: 
- Try the [JavaScript example](#javascriptnodejs) 
- Check [available streams](#available-streams)
- Monitor [credit usage](#credit-management)
