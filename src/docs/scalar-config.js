/**
 * Scalar API Documentation Configuration
 * This file contains the configuration for the Scalar API documentation interface
 */

export const getScalarConfig = (baseUrl) => {
    return {
        theme: 'purple',
        layout: 'modern',
        showSidebar: true,
        hideDownloadButton: false,
        searchHotKey: 'k',
        customCss: `
            .scalar-app {
                --scalar-color-1: #2d1b69;
                --scalar-color-2: #553c9a;
                --scalar-color-3: #6366f1;
                --scalar-color-accent: #8b5cf6;
                --scalar-background-1: #ffffff;
                --scalar-background-2: #f8fafc;
                --scalar-background-3: #e2e8f0;
            }
            .dark .scalar-app {
                --scalar-background-1: #0f172a;
                --scalar-background-2: #1e293b;
                --scalar-background-3: #334155;
            }
        `,
        metaData: {
            title: 'StalkAPI Documentation',
            description: 'Real-time KOL trading data and analytics API',
            ogDescription: 'StalkAPI provides real-time KOL (Key Opinion Leader) trading data and analytics through both REST API and WebSocket streaming.',
            ogTitle: 'StalkAPI Documentation',
            twitterCard: 'summary_large_image'
        },
        spec: {
            url: `${baseUrl}/docs/openapi.json`
        }
    };
};

export const getScalarHTML = (config) => {
    const isDev = process.env.NODE_ENV === 'development';
    const protocol = isDev ? 'http:' : 'https:';
    const redocUrl = isDev ? '/redoc/redoc.standalone.js' : `${protocol}//cdn.jsdelivr.net/npm/redoc@2.1.5/bundles/redoc.standalone.js`;

    return `<!DOCTYPE html>
<html>
<head>
    <title>StalkAPI Documentation</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content="StalkAPI - Real-time KOL trading data and analytics API documentation" />
    <meta property="og:title" content="StalkAPI Documentation" />
    <meta property="og:description" content="Real-time KOL trading data and analytics API" />
    <meta property="og:type" content="website" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        redoc {
            display: block;
        }
        /* Custom purple theme for Redoc */
        .redoc-wrap {
            --redoc-brand-color: #6366f1;
            --redoc-brand-color-dark: #4f46e5;
            --redoc-brand-color-light: #8b5cf6;
        }
        .menu-content {
            background-color: #f8fafc !important;
        }
        .api-info-wrap h1 {
            color: #6366f1 !important;
        }
        .operation-type.get {
            background-color: #10b981 !important;
        }
        .operation-type.post {
            background-color: #3b82f6 !important;
        }
        .operation-type.put {
            background-color: #f59e0b !important;
        }
        .operation-type.delete {
            background-color: #ef4444 !important;
        }
    </style>
</head>
<body>
    <redoc spec-url="/docs/openapi.json"></redoc>
    <script src="${redocUrl}"></script>
</body>
</html>`;
};
