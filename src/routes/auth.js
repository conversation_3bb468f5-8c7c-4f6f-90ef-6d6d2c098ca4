import express from 'express';
import { User } from '../models/User.js';
import { verifyToken } from '../middleware/auth.js';
import { createEndpointRateLimit } from '../middleware/rateLimiter.js';
import { getCreditStatus } from '../middleware/credits.js';

const router = express.Router();

// Rate limiting for auth endpoints - temporarily disabled for debugging
// const authRateLimit = createEndpointRateLimit(10, 15); // 10 requests per 15 minutes

// Note: Registration and login are handled by the frontend website
// These endpoints have been removed for security reasons

// Note: Refresh token endpoint removed - API now uses only API key authentication

// Get current user profile
router.get('/profile', verifyToken, async (req, res) => {
    try {
        const user = req.user;
        const creditStatus = await getCreditStatus(user.id);
        
        res.json({
            user: user,
            credits: creditStatus
        });
        
    } catch (error) {
        console.error('Profile fetch error:', error);
        res.status(500).json({
            error: 'Failed to fetch profile'
        });
    }
});

// Update user profile
router.put('/profile', verifyToken, async (req, res) => {
    try {
        const user = await User.findById(req.user.id);
        const { email } = req.body;
        
        const updateData = {};
        
        if (email && email !== user.email) {
            // Validate email
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                return res.status(400).json({
                    error: 'Invalid email format'
                });
            }
            updateData.email = email.toLowerCase();
        }
        
        if (Object.keys(updateData).length === 0) {
            return res.status(400).json({
                error: 'No valid fields to update'
            });
        }
        
        await user.update(updateData);
        
        res.json({
            message: 'Profile updated successfully',
            user: user.toJSON()
        });
        
    } catch (error) {
        console.error('Profile update error:', error);
        res.status(500).json({
            error: 'Failed to update profile'
        });
    }
});



// Get usage statistics
router.get('/usage', verifyToken, async (req, res) => {
    try {
        const { days = 30 } = req.query;
        const user = await User.findById(req.user.id);
        const stats = await user.getUsageStats(parseInt(days));
        
        res.json({
            usage: stats,
            period: `${days} days`
        });
        
    } catch (error) {
        console.error('Usage stats error:', error);
        res.status(500).json({
            error: 'Failed to fetch usage statistics'
        });
    }
});

export default router;
